<?php
declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\Constraint;

use DateInterval;
use <PERSON><PERSON><PERSON><PERSON>\Clock\Clock;
use <PERSON>cobu<PERSON>\JWT\Token;
use Lcobucci\JWT\Validation\Constraint;

/** @deprecated Use \Lcobucci\JWT\Validation\Constraint\LooseValidAt */
final class ValidAt implements Constraint
{
    private LooseValidAt $constraint;

    public function __construct(Clock $clock, ?DateInterval $leeway = null)
    {
        $this->constraint = new LooseValidAt($clock, $leeway);
    }

    public function assert(Token $token): void
    {
        $this->constraint->assert($token);
    }
}
