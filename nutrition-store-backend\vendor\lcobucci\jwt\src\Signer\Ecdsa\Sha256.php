<?php
declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Ecdsa;

use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Ecdsa;

use const OPENSSL_ALGO_SHA256;

final class Sha256 extends Ecdsa
{
    public function algorithmId(): string
    {
        return 'ES256';
    }

    public function algorithm(): int
    {
        return OPENSSL_ALGO_SHA256;
    }

    public function pointLength(): int
    {
        return 64;
    }

    public function expectedKeyLength(): int
    {
        return 256;
    }
}
