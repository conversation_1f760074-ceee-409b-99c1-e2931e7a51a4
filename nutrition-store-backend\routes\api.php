<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Authentication routes
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    Route::post('logout', [AuthController::class, 'logout']);
    Route::post('refresh', [AuthController::class, 'refresh']);
    Route::get('me', [AuthController::class, 'me']);
    Route::put('profile', [AuthController::class, 'updateProfile']);
});

// Public routes (no authentication required)
Route::get('categories', function () {
    return response()->json([
        'success' => true,
        'categories' => \App\Models\Category::active()->ordered()->get()
    ]);
});

Route::get('products', function () {
    return response()->json([
        'success' => true,
        'products' => []
    ]);
});

// Protected routes
Route::middleware('auth:api')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
});

// Public routes (no authentication required)
Route::get('health', function () {
    return response()->json([
        'status' => 'OK',
        'message' => 'Nutrition Store API is running',
        'timestamp' => now()->toISOString()
    ]);
});
