<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Workout extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'level',
        'type',
        'duration_minutes',
        'days_count',
        'equipment_needed',
        'exercises',
        'video_url',
        'video_urls',
        'thumbnail_url',
        'is_premium',
        'is_active',
        'calories_burned_estimate',
        'target_muscles',
        'instructions',
        'tips',
        'completion_count',
        'average_rating',
    ];

    protected function casts(): array
    {
        return [
            'equipment_needed' => 'array',
            'exercises' => 'array',
            'video_urls' => 'array',
            'target_muscles' => 'array',
            'is_premium' => 'boolean',
            'is_active' => 'boolean',
            'average_rating' => 'decimal:2',
        ];
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFree($query)
    {
        return $query->where('is_premium', false);
    }

    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }

    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Helper methods
    public function isAccessibleBy(User $user)
    {
        if (!$this->is_premium) {
            return true;
        }

        return $user->isPremiumUser();
    }

    public function getDurationFormatted()
    {
        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $minutes . 'm';
    }
}
