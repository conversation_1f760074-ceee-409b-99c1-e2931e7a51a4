<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class TdeeResult extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'age',
        'gender',
        'height',
        'weight',
        'activity_level',
        'fitness_goal',
        'body_fat_percentage',
        'bmr',
        'tdee',
        'target_calories',
        'macros',
        'water_intake_liters',
        'additional_data',
    ];

    protected function casts(): array
    {
        return [
            'height' => 'decimal:2',
            'weight' => 'decimal:2',
            'body_fat_percentage' => 'decimal:2',
            'bmr' => 'decimal:2',
            'tdee' => 'decimal:2',
            'target_calories' => 'decimal:2',
            'water_intake_liters' => 'decimal:2',
            'macros' => 'array',
            'additional_data' => 'array',
        ];
    }

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Helper methods
    public function getProteinGrams()
    {
        return $this->macros['protein_grams'] ?? 0;
    }

    public function getCarbsGrams()
    {
        return $this->macros['carbs_grams'] ?? 0;
    }

    public function getFatGrams()
    {
        return $this->macros['fat_grams'] ?? 0;
    }

    public function getProteinCalories()
    {
        return $this->getProteinGrams() * 4; // 4 calories per gram
    }

    public function getCarbsCalories()
    {
        return $this->getCarbsGrams() * 4; // 4 calories per gram
    }

    public function getFatCalories()
    {
        return $this->getFatGrams() * 9; // 9 calories per gram
    }

    public function getMacroPercentages()
    {
        $totalCalories = $this->target_calories;

        return [
            'protein_percent' => round(($this->getProteinCalories() / $totalCalories) * 100),
            'carbs_percent' => round(($this->getCarbsCalories() / $totalCalories) * 100),
            'fat_percent' => round(($this->getFatCalories() / $totalCalories) * 100),
        ];
    }
}
