<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workouts', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description');
            $table->enum('level', ['beginner', 'intermediate', 'advanced']);
            $table->enum('type', ['strength', 'cardio', 'hiit', 'yoga', 'flexibility', 'sport_specific', 'rehabilitation']);
            $table->integer('duration_minutes'); // Total workout duration
            $table->integer('days_count'); // Number of days in the program (7, 30, 60, 90)
            $table->json('equipment_needed')->nullable(); // Array of required equipment
            $table->json('exercises')->nullable(); // Array of exercises with details
            $table->string('video_url')->nullable(); // Main workout video
            $table->json('video_urls')->nullable(); // Array of exercise video URLs
            $table->string('thumbnail_url')->nullable();
            $table->boolean('is_premium')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('calories_burned_estimate')->nullable();
            $table->json('target_muscles')->nullable(); // Array of target muscle groups
            $table->text('instructions')->nullable();
            $table->text('tips')->nullable();
            $table->integer('completion_count')->default(0);
            $table->decimal('average_rating', 3, 2)->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workouts');
    }
};
