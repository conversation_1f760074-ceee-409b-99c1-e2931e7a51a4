<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Subscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'plan_type',
        'status',
        'amount',
        'currency',
        'stripe_subscription_id',
        'stripe_customer_id',
        'stripe_price_id',
        'current_period_start',
        'current_period_end',
        'trial_start',
        'trial_end',
        'cancelled_at',
        'expires_at',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'current_period_start' => 'datetime',
            'current_period_end' => 'datetime',
            'trial_start' => 'datetime',
            'trial_end' => 'datetime',
            'cancelled_at' => 'datetime',
            'expires_at' => 'datetime',
            'metadata' => 'array',
        ];
    }

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByPlan($query, $planType)
    {
        return $query->where('plan_type', $planType);
    }

    // Helper methods
    public function isActive()
    {
        return $this->status === 'active' &&
               $this->current_period_end &&
               $this->current_period_end->isFuture();
    }

    public function isOnTrial()
    {
        return $this->trial_end && $this->trial_end->isFuture();
    }

    public function daysUntilExpiry()
    {
        if (!$this->current_period_end) {
            return 0;
        }

        return now()->diffInDays($this->current_period_end, false);
    }

    public function cancel()
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
        ]);
    }
}
