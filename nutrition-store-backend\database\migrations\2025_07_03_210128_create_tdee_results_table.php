<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tdee_results', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->integer('age');
            $table->enum('gender', ['male', 'female', 'other']);
            $table->decimal('height', 5, 2); // in cm
            $table->decimal('weight', 5, 2); // in kg
            $table->enum('activity_level', ['sedentary', 'light', 'moderate', 'active', 'very_active']);
            $table->enum('fitness_goal', ['lose_weight', 'maintain', 'gain_muscle', 'bulk']);
            $table->decimal('body_fat_percentage', 5, 2)->nullable();
            $table->decimal('bmr', 8, 2); // Basal Metabolic Rate
            $table->decimal('tdee', 8, 2); // Total Daily Energy Expenditure
            $table->decimal('target_calories', 8, 2); // Goal-adjusted calories
            $table->json('macros'); // Protein, carbs, fat breakdown
            $table->decimal('water_intake_liters', 4, 2)->nullable();
            $table->json('additional_data')->nullable(); // Any extra calculations or notes
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tdee_results');
    }
};
