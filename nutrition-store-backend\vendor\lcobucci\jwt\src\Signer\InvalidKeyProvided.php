<?php
declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\JWT\Signer;

use InvalidArgumentException;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Exception;

final class Invalid<PERSON>eyProvided extends InvalidArgumentException implements Exception
{
    public static function cannotBeParsed(string $details): self
    {
        return new self('It was not possible to parse your key, reason:' . $details);
    }

    public static function incompatibleKeyType(string $expectedType, string $actualType): self
    {
        return new self(
            'The type of the provided key is not "' . $expectedType
            . '", "' . $actualType . '" provided'
        );
    }

    public static function incompatibleKeyLength(int $expectedLength, int $actualLength): self
    {
        return new self(
            'The length of the provided key is different than ' . $expectedLength . ' bits, '
            . $actualLength . ' bits provided'
        );
    }

    public static function cannotBeEmpty(): self
    {
        return new self('Key cannot be empty');
    }

    public static function tooShort(int $expectedLength, int $actualLength): self
    {
        return new self('Key provided is shorter than ' . $expectedLength . ' bits,'
            . ' only ' . $actualLength . ' bits provided');
    }
}
