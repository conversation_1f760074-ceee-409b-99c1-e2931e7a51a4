<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Protein',
                'slug' => 'protein',
                'description' => 'High-quality protein supplements for muscle building and recovery',
                'sort_order' => 1,
            ],
            [
                'name' => 'Performance',
                'slug' => 'performance',
                'description' => 'Pre-workout, post-workout, and performance enhancing supplements',
                'sort_order' => 2,
            ],
            [
                'name' => 'Health',
                'slug' => 'health',
                'description' => 'Vitamins, minerals, and general health supplements',
                'sort_order' => 3,
            ],
            [
                'name' => 'Energy',
                'slug' => 'energy',
                'description' => 'Energy boosters and metabolism support supplements',
                'sort_order' => 4,
            ],
            [
                'name' => 'Recovery',
                'slug' => 'recovery',
                'description' => 'Post-workout recovery and muscle repair supplements',
                'sort_order' => 5,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
