{"name": "tymon/jwt-auth", "description": "JSON Web Token Authentication for <PERSON><PERSON> and <PERSON><PERSON>", "keywords": ["auth", "authentication", "json web token", "jwt", "laravel"], "homepage": "https://github.com/tymondesigns/jwt-auth", "support": {"issues": "https://github.com/tymondesigns/jwt-auth/issues", "source": "https://github.com/tymondesigns/jwt-auth"}, "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://tymon.xyz", "role": "Developer"}], "require": {"php": "^8.0", "illuminate/auth": "^9.0|^10.0|^11.0|^12.0", "illuminate/contracts": "^9.0|^10.0|^11.0|^12.0", "illuminate/http": "^9.0|^10.0|^11.0|^12.0", "illuminate/support": "^9.0|^10.0|^11.0|^12.0", "lcobucci/jwt": "^4.0", "nesbot/carbon": "^2.69|^3.0"}, "require-dev": {"illuminate/console": "^9.0|^10.0|^11.0|^12.0", "illuminate/database": "^9.0|^10.0|^11.0|^12.0", "illuminate/routing": "^9.0|^10.0|^11.0|^12.0", "mockery/mockery": "^1.6", "phpunit/phpunit": "^9.4"}, "autoload": {"psr-4": {"Tymon\\JWTAuth\\": "src/"}}, "autoload-dev": {"psr-4": {"Tymon\\JWTAuth\\Test\\": "tests/"}}, "extra": {"branch-alias": {"dev-develop": "1.0-dev", "dev-2.x": "2.0-dev"}, "laravel": {"aliases": {"JWTAuth": "Tymon\\JWTAuth\\Facades\\JWTAuth", "JWTFactory": "Tymon\\JWTAuth\\Facades\\JWTFactory"}, "providers": ["Tymon\\JWTAuth\\Providers\\LaravelServiceProvider"]}}, "config": {"sort-packages": true}, "prefer-stable": true, "minimum-stability": "dev", "scripts": {"test": "phpunit --colors=always", "test:ci": "composer test -- --verbose --coverage-text --coverage-clover=coverage.xml"}}