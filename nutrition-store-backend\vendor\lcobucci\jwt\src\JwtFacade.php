<?php
declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\JWT;

use Closure;
use DateTimeImmutable;
use <PERSON><PERSON><PERSON><PERSON>\Clock\Clock;
use <PERSON><PERSON><PERSON><PERSON>\Clock\SystemClock;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Encoding\ChainedFormatter;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Encoding\JoseEncoder;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key;
use <PERSON><PERSON>bu<PERSON>\JWT\Validation\Constraint;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\SignedWith;
use <PERSON><PERSON><PERSON>cci\JWT\Validation\ValidAt;
use <PERSON><PERSON><PERSON>cci\JWT\Validation\Validator;

use function assert;

final class JwtFacade
{
    private Parser $parser;
    private Clock $clock;

    public function __construct(?Parser $parser = null, ?Clock $clock = null)
    {
        $this->parser = $parser ?? new Token\Parser(new JoseEncoder());
        $this->clock  = $clock ?? SystemClock::fromSystemTimezone();
    }

    /** @param Closure(Builder, DateTimeImmutable):Builder $customiseBuilder */
    public function issue(
        Signer $signer,
        Key $signingKey,
        Closure $customiseBuilder
    ): UnencryptedToken {
        $builder = new Token\Builder(new JoseEncoder(), ChainedFormatter::withUnixTimestampDates());

        $now = $this->clock->now();
        $builder
            ->issuedAt($now)
            ->canOnlyBeUsedAfter($now)
            ->expiresAt($now->modify('+5 minutes'));

        return $customiseBuilder($builder, $now)->getToken($signer, $signingKey);
    }

    public function parse(
        string $jwt,
        SignedWith $signedWith,
        ValidAt $validAt,
        Constraint ...$constraints
    ): UnencryptedToken {
        $token = $this->parser->parse($jwt);
        assert($token instanceof UnencryptedToken);

        (new Validator())->assert(
            $token,
            $signedWith,
            $validAt,
            ...$constraints
        );

        return $token;
    }
}
